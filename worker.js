const http = require('http');
const url = require('url');
const fs = require('fs');
const { execSync } = require('child_process');
const client = require('prom-client');

// 创建一个注册表
const register = new client.Registry();

// 启用默认指标，并指定注册表
client.collectDefaultMetrics({ register });

// 设置聚合器注册表，使master能够收集worker的指标
client.AggregatorRegistry.setRegistries([register]);

// 创建一个 Gauge 指标
const activeUsersGauge = new client.Gauge({
    name: 'app_active_users',
    help: 'Number of active users currently logged in',
    labelNames: ['worker_id'],
    registers: [register],
});

// 创建一个 Counter 指标
const apiRequestsCounter = new client.Counter({
    name: 'app_api_requests_total',
    help: 'Total number of API requests',
    labelNames: ['method', 'path', 'status', 'worker_id'],
    registers: [register],
});

// 创建进程文件句柄数指标
const processFilesOpenGauge = new client.Gauge({
    name: 'process_files_open_files',
    help: 'Number of open file descriptors/handles by the process',
    labelNames: ['worker_id'],
    registers: [register],
    collect() {
        // 获取当前进程的文件句柄数
        const openFiles = getOpenFileDescriptors();
        this.set({ worker_id: process.pid.toString() }, openFiles);
    },
});

// 获取进程打开的文件描述符数量
function getOpenFileDescriptors() {
    try {
        if (process.platform === 'linux') {
            // Linux: 读取 /proc/self/fd 目录
            const fds = fs.readdirSync('/proc/self/fd');
            return fds.length;
        } else if (process.platform === 'darwin') {
            // macOS: 使用 lsof 命令
            const output = execSync(`lsof -p ${process.pid} | wc -l`, { encoding: 'utf8' });
            // lsof 输出包含标题行，所以减1
            return Math.max(0, parseInt(output.trim()) - 1);
        } else if (process.platform === 'win32') {
            // Windows: 使用 process._getActiveHandles() 和 process._getActiveRequests()
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        } else {
            // 其他平台：返回一个估算值
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        }
    } catch (error) {
        // 如果获取失败，返回0或使用备用方法
        console.warn('Failed to get open file descriptors:', error.message);
        try {
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        } catch {
            return 0;
        }
    }
}

// 模拟数据变化
let activeUsers = 0;
setInterval(() => {
    activeUsers = Math.floor(Math.random() * 100);
    activeUsersGauge.set({ worker_id: process.pid.toString() }, activeUsers);
}, 5000);

// 辅助函数：发送JSON响应
function sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
    });
    res.end(JSON.stringify(data));
}

// 辅助函数：发送文本响应
function sendText(res, data, contentType = 'text/plain', statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*'
    });
    res.end(data);
}

// 辅助函数：发送404响应
function send404(res) {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
}

// 获取聚合metrics的函数
async function getAggregatedMetrics() {
    try {
        // 在cluster模式下，每个worker都可以获取聚合指标
        const aggregatorRegistry = new client.AggregatorRegistry();
        const metrics = await aggregatorRegistry.clusterMetrics();

        // 如果聚合成功且有内容，返回聚合指标
        if (metrics && metrics.trim().length > 0) {
            return metrics;
        } else {
            throw new Error('Empty aggregated metrics');
        }
    } catch (error) {
        console.warn(`Worker ${process.pid} failed to get aggregated metrics:`, error.message);
        // 如果聚合失败，返回当前worker的指标
        const workerMetrics = await register.metrics();
        return `# Aggregated metrics unavailable, showing worker ${process.pid} metrics only\n${workerMetrics}`;
    }
}

// 获取cluster状态的函数
function getClusterStatus() {
    // Worker进程无法直接获取其他worker信息，返回当前worker信息
    return {
        worker_pid: process.pid,
        status: 'This endpoint shows limited info from worker. Use master process for full cluster status.',
        worker_info: {
            pid: process.pid,
            uptime: process.uptime(),
            memory_usage: process.memoryUsage()
        }
    };
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    try {
        // 路由处理
        if (method === 'GET' && path === '/api/data') {
            // API 数据路由
            apiRequestsCounter.labels('GET', '/api/data', '200', process.pid.toString()).inc();
            sendJSON(res, {
                message: 'Hello World',
                worker_id: process.pid,
                timestamp: new Date().toISOString()
            });
        } else if (method === 'GET' && path === '/health') {
            // 健康检查路由
            sendJSON(res, {
                status: 'healthy',
                worker_id: process.pid,
                uptime: process.uptime()
            });
        } else if (method === 'GET' && path === '/metrics') {
            // 聚合所有worker的指标
            const metrics = await getAggregatedMetrics();
            sendText(res, metrics, 'text/plain; version=0.0.4; charset=utf-8');
        } else if (method === 'GET' && path === '/cluster-status') {
            // 集群状态信息
            const status = getClusterStatus();
            sendJSON(res, status);
        } else if (method === 'GET' && path === '/worker-metrics') {
            // 单个worker的指标（调试用）
            const metrics = await register.metrics();
            sendText(res, metrics, register.contentType);
        } else {
            // 404 处理
            send404(res);
        }
    } catch (error) {
        // 错误处理
        console.error(`Worker ${process.pid} error:`, error);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
    }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`Worker ${process.pid} running on http://localhost:${PORT}`);
});

// 优雅关闭处理
process.on('SIGTERM', () => {
    console.log(`Worker ${process.pid} received SIGTERM, shutting down gracefully`);
    server.close(() => {
        console.log(`Worker ${process.pid} closed`);
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log(`Worker ${process.pid} received SIGINT, shutting down gracefully`);
    server.close(() => {
        console.log(`Worker ${process.pid} closed`);
        process.exit(0);
    });
});

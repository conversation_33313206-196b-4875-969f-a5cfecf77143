const http = require('http');
const url = require('url');
const fs = require('fs');
const { execSync } = require('child_process');
const client = require('prom-client');

// 创建一个注册表
const register = new client.Registry();

// 启用默认指标，并指定注册表
client.collectDefaultMetrics({ register });

// 设置聚合器注册表，使master能够收集worker的指标
client.AggregatorRegistry.setRegistries([register]);

// 创建一个 Gauge 指标
const activeUsersGauge = new client.Gauge({
    name: 'app_active_users',
    help: 'Number of active users currently logged in',
    registers: [register],
});

// 创建一个 Counter 指标
const apiRequestsCounter = new client.Counter({
    name: 'app_api_requests_total',
    help: 'Total number of API requests',
    labelNames: ['method', 'path', 'status', 'worker_id'],
    registers: [register],
});

// 创建进程文件句柄数指标
const processFilesOpenGauge = new client.Gauge({
    name: 'process_files_open_files',
    help: 'Number of open file descriptors/handles by the process',
    labelNames: ['worker_id'],
    registers: [register],
    collect() {
        // 获取当前进程的文件句柄数
        const openFiles = getOpenFileDescriptors();
        this.set({ worker_id: process.pid.toString() }, openFiles);
    },
});

// 获取进程打开的文件描述符数量
function getOpenFileDescriptors() {
    try {
        if (process.platform === 'linux') {
            // Linux: 读取 /proc/self/fd 目录
            const fds = fs.readdirSync('/proc/self/fd');
            return fds.length;
        } else if (process.platform === 'darwin') {
            // macOS: 使用 lsof 命令
            const output = execSync(`lsof -p ${process.pid} | wc -l`, { encoding: 'utf8' });
            // lsof 输出包含标题行，所以减1
            return Math.max(0, parseInt(output.trim()) - 1);
        } else if (process.platform === 'win32') {
            // Windows: 使用 process._getActiveHandles() 和 process._getActiveRequests()
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        } else {
            // 其他平台：返回一个估算值
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        }
    } catch (error) {
        // 如果获取失败，返回0或使用备用方法
        console.warn('Failed to get open file descriptors:', error.message);
        try {
            const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
            const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
            return handles + requests;
        } catch {
            return 0;
        }
    }
}

// 模拟数据变化
let activeUsers = 0;
setInterval(() => {
    activeUsers = Math.floor(Math.random() * 100);
    activeUsersGauge.set(activeUsers);
}, 5000);

// 辅助函数：发送JSON响应
function sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
    });
    res.end(JSON.stringify(data));
}

// 辅助函数：发送文本响应
function sendText(res, data, contentType = 'text/plain', statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*'
    });
    res.end(data);
}

// 辅助函数：发送404响应
function send404(res) {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    try {
        // 路由处理
        if (method === 'GET' && path === '/api/data') {
            // API 数据路由
            apiRequestsCounter.labels('GET', '/api/data', '200', process.pid.toString()).inc();
            sendJSON(res, { 
                message: 'Hello World', 
                worker_id: process.pid,
                timestamp: new Date().toISOString()
            });
        } else if (method === 'GET' && path === '/metrics') {
            // 指标路由 - 在cluster模式下，这个路由通常不会被直接访问
            // 因为metrics会通过master进程聚合
            const metrics = await register.metrics();
            sendText(res, metrics, register.contentType);
        } else if (method === 'GET' && path === '/health') {
            // 健康检查路由
            sendJSON(res, { 
                status: 'healthy', 
                worker_id: process.pid,
                uptime: process.uptime()
            });
        } else {
            // 404 处理
            send404(res);
        }
    } catch (error) {
        // 错误处理
        console.error(`Worker ${process.pid} error:`, error);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
    }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`Worker ${process.pid} running on http://localhost:${PORT}`);
});

// 处理来自master的消息
process.on('message', async (message) => {
    if (message.type === 'get-metrics') {
        try {
            const metrics = await register.metrics();
            process.send({
                type: 'metrics-response',
                data: metrics
            });
        } catch (error) {
            console.error(`Worker ${process.pid} failed to get metrics:`, error);
            process.send({
                type: 'metrics-response',
                data: ''
            });
        }
    }
});

// 优雅关闭处理
process.on('SIGTERM', () => {
    console.log(`Worker ${process.pid} received SIGTERM, shutting down gracefully`);
    server.close(() => {
        console.log(`Worker ${process.pid} closed`);
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log(`Worker ${process.pid} received SIGINT, shutting down gracefully`);
    server.close(() => {
        console.log(`Worker ${process.pid} closed`);
        process.exit(0);
    });
});

const cluster = require('cluster');
const os = require('os');
const http = require('http');
const url = require('url');
const client = require('prom-client');

// 获取CPU核心数，默认启动对应数量的worker
const numCPUs = os.cpus().length;
const numWorkers = process.env.WORKERS || Math.min(numCPUs, 4); // 限制最大worker数量

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);
    console.log(`Starting ${numWorkers} workers...`);

    // 创建聚合注册表用于收集所有worker的指标
    const aggregatorRegistry = new client.AggregatorRegistry();

    // Fork workers
    for (let i = 0; i < numWorkers; i++) {
        const worker = cluster.fork();
        console.log(`Worker ${worker.process.pid} started`);
    }

    // 监听worker退出事件
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
        console.log('Starting a new worker...');
        const newWorker = cluster.fork();
        console.log(`New worker ${newWorker.process.pid} started`);
    });

    // 聚合所有worker指标的函数
    async function getAggregatedMetrics() {
        try {
            return await aggregatorRegistry.clusterMetrics();
        } catch (error) {
            console.warn('Failed to get aggregated metrics:', error.message);
            return '# No metrics available\n';
        }
    }

    const PORT = process.env.PORT || 3001;
    console.log(`Master ${process.pid} managing workers on port ${PORT}`);
    console.log(`Workers will handle all HTTP requests including:`);
    console.log(`  - http://localhost:${PORT}/api/data (API requests)`);
    console.log(`  - http://localhost:${PORT}/health (health check)`);
    console.log(`  - http://localhost:${PORT}/metrics (aggregated metrics)`);
    console.log(`  - http://localhost:${PORT}/cluster-status (cluster info)`);

    // 优雅关闭处理
    process.on('SIGTERM', () => {
        console.log('Master received SIGTERM, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGTERM');
        }

        console.log('Master shutting down');
        process.exit(0);
    });

    process.on('SIGINT', () => {
        console.log('Master received SIGINT, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGINT');
        }

        console.log('Master shutting down');
        process.exit(0);
    });

} else {
    // Worker进程
    require('./worker.js');
}
const cluster = require('cluster');
const os = require('os');
const http = require('http');
const url = require('url');
const client = require('prom-client');

// 获取CPU核心数，默认启动对应数量的worker
const numCPUs = os.cpus().length;
const numWorkers = process.env.WORKERS || Math.min(numCPUs, 4); // 限制最大worker数量

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);
    console.log(`Starting ${numWorkers} workers...`);

    // 创建聚合注册表用于收集所有worker的指标
    const aggregatorRegistry = new client.AggregatorRegistry();

    // Fork workers
    for (let i = 0; i < numWorkers; i++) {
        const worker = cluster.fork();
        console.log(`Worker ${worker.process.pid} started`);

        // 为每个worker添加消息监听器
        worker.on('message', (message) => {
            handleWorkerMessage(worker, message);
        });
    }

    // 监听worker退出事件
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
        console.log('Starting a new worker...');
        const newWorker = cluster.fork();
        console.log(`New worker ${newWorker.process.pid} started`);

        // 为新worker添加消息监听器
        newWorker.on('message', (message) => {
            handleWorkerMessage(newWorker, message);
        });
    });

    // 处理来自worker的消息
    function handleWorkerMessage(worker, message) {
        if (message.type === 'request-aggregated-metrics') {
            // Worker请求聚合指标
            getAggregatedMetrics()
                .then(metrics => {
                    worker.send({
                        type: 'aggregated-metrics-response',
                        requestId: message.requestId,
                        metrics: metrics,
                        success: true
                    });
                })
                .catch(error => {
                    console.error('Failed to get aggregated metrics:', error);
                    worker.send({
                        type: 'aggregated-metrics-response',
                        requestId: message.requestId,
                        metrics: `# Error getting aggregated metrics: ${error.message}\n`,
                        success: false
                    });
                });
        } else if (message.type === 'get-worker-metrics') {
            // Master请求worker的指标（用于备用聚合）
            // 这个消息实际上是worker发给自己的，这里不需要处理
            // 实际的处理在worker.js中
        }
    }

    // 聚合所有worker指标的函数
    async function getAggregatedMetrics() {
        try {
            // 使用更短的超时时间
            const metrics = await Promise.race([
                aggregatorRegistry.clusterMetrics(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Operation timed out.')), 3000)
                )
            ]);
            return metrics || '# No metrics available\n';
        } catch (error) {
            console.warn('Failed to get aggregated metrics:', error.message);

            // 备用方案：直接从worker获取指标
            try {
                const workerMetrics = await getWorkerMetrics();
                return `# Aggregated metrics failed, showing combined worker metrics\n${workerMetrics}`;
            } catch (fallbackError) {
                return `# Error: ${error.message}\n# Fallback also failed: ${fallbackError.message}\n`;
            }
        }
    }

    // 备用方案：直接从worker获取指标
    async function getWorkerMetrics() {
        const promises = [];

        for (const id in cluster.workers) {
            if (cluster.workers[id].isConnected()) {
                const promise = new Promise((resolve) => {
                    const worker = cluster.workers[id];
                    const timeout = setTimeout(() => {
                        resolve(''); // 超时返回空字符串
                    }, 2000);

                    const requestId = Date.now() + Math.random();

                    // 发送获取metrics的请求
                    worker.send({ type: 'get-worker-metrics', requestId });

                    // 监听响应
                    const handler = (message) => {
                        if (message.type === 'worker-metrics-response' && message.requestId === requestId) {
                            clearTimeout(timeout);
                            worker.removeListener('message', handler);
                            resolve(message.data || '');
                        }
                    };
                    worker.on('message', handler);
                });
                promises.push(promise);
            }
        }

        const results = await Promise.all(promises);
        return results.filter(r => r).join('\n');
    }

    const PORT = process.env.PORT || 3001;
    console.log(`Master ${process.pid} managing workers on port ${PORT}`);
    console.log(`Workers will handle all HTTP requests including:`);
    console.log(`  - http://localhost:${PORT}/api/data (API requests)`);
    console.log(`  - http://localhost:${PORT}/health (health check)`);
    console.log(`  - http://localhost:${PORT}/metrics (aggregated metrics)`);
    console.log(`  - http://localhost:${PORT}/cluster-status (cluster info)`);

    // 优雅关闭处理
    process.on('SIGTERM', () => {
        console.log('Master received SIGTERM, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGTERM');
        }

        console.log('Master shutting down');
        process.exit(0);
    });

    process.on('SIGINT', () => {
        console.log('Master received SIGINT, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGINT');
        }

        console.log('Master shutting down');
        process.exit(0);
    });

} else {
    // Worker进程
    require('./worker.js');
}
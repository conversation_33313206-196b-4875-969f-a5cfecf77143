const cluster = require('cluster');
const os = require('os');
const http = require('http');
const url = require('url');
const client = require('prom-client');

// 获取CPU核心数，默认启动对应数量的worker
const numCPUs = os.cpus().length;
const numWorkers = process.env.WORKERS || numCPUs;

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);
    console.log(`Starting ${numWorkers} workers...`);

    // 创建聚合注册表用于收集所有worker的指标
    const aggregatorRegistry = new client.AggregatorRegistry();

    // 简单的metrics聚合函数
    async function getAggregatedMetrics() {
        try {
            return await aggregatorRegistry.clusterMetrics();
        } catch (error) {
            console.warn('AggregatorRegistry failed, falling back to simple aggregation:', error.message);

            // 备用方案：直接从worker获取metrics
            const workerMetrics = [];
            const promises = [];

            for (const id in cluster.workers) {
                if (cluster.workers[id].isConnected()) {
                    const promise = new Promise((resolve) => {
                        const worker = cluster.workers[id];
                        const timeout = setTimeout(() => {
                            resolve(''); // 超时返回空字符串
                        }, 2000);

                        // 发送获取metrics的请求
                        worker.send({ type: 'get-metrics' });

                        // 监听响应
                        const handler = (message) => {
                            if (message.type === 'metrics-response') {
                                clearTimeout(timeout);
                                worker.removeListener('message', handler);
                                resolve(message.data || '');
                            }
                        };
                        worker.on('message', handler);
                    });
                    promises.push(promise);
                }
            }

            const results = await Promise.all(promises);
            return results.filter(r => r).join('\n');
        }
    }

    // Fork workers
    for (let i = 0; i < numWorkers; i++) {
        const worker = cluster.fork();
        console.log(`Worker ${worker.process.pid} started`);
    }

    // 监听worker退出事件
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
        console.log('Starting a new worker...');
        const newWorker = cluster.fork();
        console.log(`New worker ${newWorker.process.pid} started`);
    });

    // Master进程的HTTP服务器，主要用于聚合metrics
    const masterServer = http.createServer(async (req, res) => {
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        const method = req.method;

        try {
            if (method === 'GET' && path === '/metrics') {
                // 聚合所有worker的指标
                const metrics = await getAggregatedMetrics();
                res.writeHead(200, {
                    'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(metrics);
            } else if (method === 'GET' && path === '/cluster-status') {
                // 集群状态信息
                const workers = Object.values(cluster.workers).map(worker => ({
                    id: worker.id,
                    pid: worker.process.pid,
                    state: worker.state,
                    isDead: worker.isDead()
                }));

                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                    master_pid: process.pid,
                    workers: workers,
                    total_workers: workers.length,
                    active_workers: workers.filter(w => !w.isDead).length
                }));
            } else {
                // 其他请求转发给worker处理（负载均衡）
                // 这里我们返回一个简单的响应，实际应用中可能需要更复杂的负载均衡
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                    message: 'This is the master process. API requests are handled by workers.',
                    master_pid: process.pid,
                    available_endpoints: ['/metrics', '/cluster-status'],
                    worker_endpoints: ['/api/data', '/health']
                }));
            }
        } catch (error) {
            console.error('Master server error:', error);
            res.writeHead(500, { 'Content-Type': 'text/plain' });
            res.end('Internal Server Error');
        }
    });

    const MASTER_PORT = 3000; // Master进程监听不同端口
    masterServer.listen(MASTER_PORT, () => {
        console.log(`Master server running on http://localhost:${MASTER_PORT}`);
        console.log(`Aggregated metrics available at http://localhost:${MASTER_PORT}/metrics`);
        console.log(`Cluster status available at http://localhost:${MASTER_PORT}/cluster-status`);
    });

    // 优雅关闭处理
    process.on('SIGTERM', () => {
        console.log('Master received SIGTERM, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGTERM');
        }

        // 关闭master服务器
        masterServer.close(() => {
            console.log('Master server closed');
            process.exit(0);
        });
    });

    process.on('SIGINT', () => {
        console.log('Master received SIGINT, shutting down gracefully');

        // 关闭所有worker
        for (const id in cluster.workers) {
            cluster.workers[id].kill('SIGINT');
        }

        // 关闭master服务器
        masterServer.close(() => {
            console.log('Master server closed');
            process.exit(0);
        });
    });

} else {
    // Worker进程
    require('./worker.js');
}
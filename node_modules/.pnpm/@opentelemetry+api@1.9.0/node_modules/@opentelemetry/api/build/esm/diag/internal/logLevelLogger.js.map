{"version": 3, "file": "logLevelLogger.js", "sourceRoot": "", "sources": ["../../../../src/diag/internal/logLevelLogger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAA+B,YAAY,EAAE,MAAM,UAAU,CAAC;AAErE,MAAM,UAAU,wBAAwB,CACtC,QAAsB,EACtB,MAAkB;IAElB,IAAI,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE;QAChC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC;KAC9B;SAAM,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE;QACtC,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC;KAC7B;IAED,0CAA0C;IAC1C,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IAEtB,SAAS,WAAW,CAClB,QAA0B,EAC1B,QAAsB;QAEtB,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,OAAO,OAAO,KAAK,UAAU,IAAI,QAAQ,IAAI,QAAQ,EAAE;YACzD,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7B;QACD,OAAO,cAAa,CAAC,CAAC;IACxB,CAAC;IAED,OAAO;QACL,KAAK,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC;QAC/C,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC;QAC5C,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC;QAC5C,KAAK,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC;QAC/C,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;KACtD,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogFunction, DiagLogger, DiagLogLevel } from '../types';\n\nexport function createLogLevelDiagLogger(\n  maxLevel: DiagLogLevel,\n  logger: DiagLogger\n): DiagLogger {\n  if (maxLevel < DiagLogLevel.NONE) {\n    maxLevel = DiagLogLevel.NONE;\n  } else if (maxLevel > DiagLogLevel.ALL) {\n    maxLevel = DiagLogLevel.ALL;\n  }\n\n  // In case the logger is null or undefined\n  logger = logger || {};\n\n  function _filterFunc(\n    funcName: keyof DiagLogger,\n    theLevel: DiagLogLevel\n  ): DiagLogFunction {\n    const theFunc = logger[funcName];\n\n    if (typeof theFunc === 'function' && maxLevel >= theLevel) {\n      return theFunc.bind(logger);\n    }\n    return function () {};\n  }\n\n  return {\n    error: _filterFunc('error', DiagLogLevel.ERROR),\n    warn: _filterFunc('warn', DiagLogLevel.WARN),\n    info: _filterFunc('info', DiagLogLevel.INFO),\n    debug: _filterFunc('debug', DiagLogLevel.DEBUG),\n    verbose: _filterFunc('verbose', DiagLogLevel.VERBOSE),\n  };\n}\n"]}
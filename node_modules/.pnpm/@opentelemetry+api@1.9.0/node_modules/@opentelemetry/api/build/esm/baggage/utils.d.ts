import { Baggage, BaggageEntry, BaggageEntryMetadata } from './types';
/**
 * Create a new Baggage with optional entries
 *
 * @param entries An array of baggage entries the new baggage should contain
 */
export declare function createBaggage(entries?: Record<string, BaggageEntry>): Baggage;
/**
 * Create a serializable BaggageEntryMetadata object from a string.
 *
 * @param str string metadata. Format is currently not defined by the spec and has no special meaning.
 *
 */
export declare function baggageEntryMetadataFromString(str: string): BaggageEntryMetadata;
//# sourceMappingURL=utils.d.ts.map
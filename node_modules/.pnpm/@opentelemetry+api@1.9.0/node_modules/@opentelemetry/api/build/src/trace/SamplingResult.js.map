{"version": 3, "file": "SamplingResult.js", "sourceRoot": "", "sources": ["../../../src/trace/SamplingResult.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH;;;;GAIG;AACH,IAAY,gBAgBX;AAhBD,WAAY,gBAAgB;IAC1B;;;OAGG;IACH,mEAAU,CAAA;IACV;;;OAGG;IACH,2DAAM,CAAA;IACN;;;OAGG;IACH,mFAAkB,CAAA;AACpB,CAAC,EAhBW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAgB3B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanAttributes } from './attributes';\nimport { TraceState } from './trace_state';\n\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport enum SamplingDecision {\n  /**\n   * `Span.isRecording() === false`, span will not be recorded and all events\n   * and attributes will be dropped.\n   */\n  NOT_RECORD,\n  /**\n   * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n   * MUST NOT be set.\n   */\n  RECORD,\n  /**\n   * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n   * MUST be set.\n   */\n  RECORD_AND_SAMPLED,\n}\n\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling result contains a decision for a {@link Span} and additional\n * attributes the sampler would like to added to the Span.\n */\nexport interface SamplingResult {\n  /**\n   * A sampling decision, refer to {@link SamplingDecision} for details.\n   */\n  decision: SamplingDecision;\n  /**\n   * The list of attributes returned by SamplingResult MUST be immutable.\n   * Caller may call {@link Sampler}.shouldSample any number of times and\n   * can safely cache the returned value.\n   */\n  attributes?: Readonly<SpanAttributes>;\n  /**\n   * A {@link TraceState} that will be associated with the {@link Span} through\n   * the new {@link SpanContext}. Samplers SHOULD return the TraceState from\n   * the passed-in {@link Context} if they do not intend to change it. Leaving\n   * the value undefined will also leave the TraceState unchanged.\n   */\n  traceState?: TraceState;\n}\n"]}
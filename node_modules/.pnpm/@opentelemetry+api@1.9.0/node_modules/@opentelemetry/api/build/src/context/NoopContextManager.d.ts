import * as types from './types';
export declare class NoopContextManager implements types.ContextManager {
    active(): types.Context;
    with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(_context: types.Context, fn: F, thisArg?: ThisParameterType<F>, ...args: A): ReturnType<F>;
    bind<T>(_context: types.Context, target: T): T;
    enable(): this;
    disable(): this;
}
//# sourceMappingURL=NoopContextManager.d.ts.map
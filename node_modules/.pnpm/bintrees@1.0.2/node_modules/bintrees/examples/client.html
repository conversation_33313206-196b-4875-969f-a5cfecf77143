<!doctype html>
<html>
<head>
    <title>simple browser test</title>
    <script src="../dist/rbtree.js"></script>
    <script src="../dist/bintree.js"></script>
    <script>
        function test(tree) {
            var tree = new tree(function(a,b) { return a - b; });
            tree.insert(1);
            tree.insert(2);
            tree.insert(3);
            tree.remove(2);
            tree.each(function(d) {
                console.log(d);
            });
        }
        test(RBTree);
        test(BinTree);
    </script>
</head>
<body>
    This test just makes sure the script loads and <em>something</em> works. More comprehensive tests are located in the /test directory.
</body>
</html>

BinTree=function(m){var k=function(h){h=k.m[h];if(h.mod)return h.mod.exports;var l=h.mod={exports:{}};h(l,l.exports);return l.exports};k.m={};k.m["./treebase"]=function(h,k){function f(){}function g(a){this._tree=a;this._ancestors=[];this._cursor=null}f.prototype.clear=function(){this._root=null;this.size=0};f.prototype.find=function(a){for(var c=this._root;null!==c;){var b=this._comparator(a,c.data);if(0===b)return c.data;c=c.get_child(0<b)}return null};f.prototype.findIter=function(a){for(var c=
this._root,b=this.iterator();null!==c;){var d=this._comparator(a,c.data);if(0===d)return b._cursor=c,b;b._ancestors.push(c);c=c.get_child(0<d)}return null};f.prototype.lowerBound=function(a){for(var c=this._root,b=this.iterator(),d=this._comparator;null!==c;){var e=d(a,c.data);if(0===e)return b._cursor=c,b;b._ancestors.push(c);c=c.get_child(0<e)}for(e=b._ancestors.length-1;0<=e;--e)if(c=b._ancestors[e],0>d(a,c.data))return b._cursor=c,b._ancestors.length=e,b;b._ancestors.length=0;return b};f.prototype.upperBound=
function(a){for(var c=this.lowerBound(a),b=this._comparator;null!==c.data()&&0===b(c.data(),a);)c.next();return c};f.prototype.min=function(){var a=this._root;if(null===a)return null;for(;null!==a.left;)a=a.left;return a.data};f.prototype.max=function(){var a=this._root;if(null===a)return null;for(;null!==a.right;)a=a.right;return a.data};f.prototype.iterator=function(){return new g(this)};f.prototype.each=function(a){for(var c=this.iterator(),b;null!==(b=c.next());)a(b)};f.prototype.reach=function(a){for(var c=
this.iterator(),b;null!==(b=c.prev());)a(b)};g.prototype.data=function(){return null!==this._cursor?this._cursor.data:null};g.prototype.next=function(){if(null===this._cursor){var a=this._tree._root;null!==a&&this._minNode(a)}else if(null===this._cursor.right){do if(a=this._cursor,this._ancestors.length)this._cursor=this._ancestors.pop();else{this._cursor=null;break}while(this._cursor.right===a)}else this._ancestors.push(this._cursor),this._minNode(this._cursor.right);return null!==this._cursor?this._cursor.data:
null};g.prototype.prev=function(){if(null===this._cursor){var a=this._tree._root;null!==a&&this._maxNode(a)}else if(null===this._cursor.left){do if(a=this._cursor,this._ancestors.length)this._cursor=this._ancestors.pop();else{this._cursor=null;break}while(this._cursor.left===a)}else this._ancestors.push(this._cursor),this._maxNode(this._cursor.left);return null!==this._cursor?this._cursor.data:null};g.prototype._minNode=function(a){for(;null!==a.left;)this._ancestors.push(a),a=a.left;this._cursor=
a};g.prototype._maxNode=function(a){for(;null!==a.right;)this._ancestors.push(a),a=a.right;this._cursor=a};h.exports=f};k.m.__main__=function(h,l){function f(a){this.data=a;this.right=this.left=null}function g(a){this._root=null;this._comparator=a;this.size=0}var a=k("./treebase");f.prototype.get_child=function(a){return a?this.right:this.left};f.prototype.set_child=function(a,b){a?this.right=b:this.left=b};g.prototype=new a;g.prototype.insert=function(a){if(null===this._root)return this._root=new f(a),
this.size++,!0;for(var b=0,d=null,e=this._root;;){if(null===e)return e=new f(a),d.set_child(b,e),ret=!0,this.size++,!0;if(0===this._comparator(e.data,a))return!1;b=0>this._comparator(e.data,a);d=e;e=e.get_child(b)}};g.prototype.remove=function(a){if(null===this._root)return!1;var b=new f(void 0),d=b;d.right=this._root;for(var e=null,g=null,h=1;null!==d.get_child(h);){var e=d,d=d.get_child(h),k=this._comparator(a,d.data),h=0<k;0===k&&(g=d)}return null!==g?(g.data=d.data,e.set_child(e.right===d,d.get_child(null===
d.left)),this._root=b.right,this.size--,!0):!1};h.exports=g};return k("__main__")}(window);

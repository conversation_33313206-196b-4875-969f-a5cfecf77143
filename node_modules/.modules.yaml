hoistPattern:
  - '*'
hoistedDependencies:
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  bintrees@1.0.2:
    bintrees: private
  tdigest@0.1.2:
    tdigest: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Mon, 11 Aug 2025 02:13:34 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
